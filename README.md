# 远程控制应用 (代号: "Project Gemini")

## 1. 项目概述

"Project Gemini" 是一个跨平台的远程控制应用，旨在提供一个安全、低延迟、功能丰富的远程桌面和移动设备管理解决方案。它支持 Windows、macOS、Android 和 iOS，让用户可以随时随地访问和管理自己的设备。

## 2. 核心功能

*   **实时屏幕共享**: 低延迟地将设备屏幕流式传输到控制端。
*   **远程输入控制**: 支持鼠标、键盘和触摸屏的远程操作。
*   **安全文件传输**: 实现双向、可拖拽的文件传输，并支持断点续传。
*   **设备发现与连接**: 通过唯一的设备 ID 或二维码快速配对和连接。
*   **多会话管理**: 允许同时控制多台设备，并在会话之间无缝切换。
*   **基础功能**:
    *   剪贴板同步
    *   会话录制
    *   远程重启

## 3. 技术架构

*   **传输协议**:
    *   主要: WebRTC (P2P)
    *   备用: TCP/UDP (通过 TURN 中继)
*   **屏幕采集**:
    *   Windows: DXGI API
    *   macOS: X11 API
    *   Android: MediaProjection API
    *   iOS: ReplayKit
*   **输入控制**:
    *   Windows: `SendInput`
    *   macOS: `IOHIDPostEvent`
    *   Android: AccessibilityService
*   **压缩**: H.264 (视频) + OPUS (音频)
*   **安全**: TLS 1.3 + SRP 6a

## 4. 架构设计

本项目采用微服务架构以提高可伸缩性和可靠性。

*   **信令服务器**: 使用 WebSocket (Go 实现) 管理连接建立和会话状态。
*   **中继服务器**: Coturn (TURN) 集群，用于 NAT 穿透。
*   **Web API**: 提供用户管理、设备注册等功能。

## 5. 开发路线图

1.  **阶段一: 核心框架**
    *   [x] 设计项目结构
    *   [ ] 实现信令服务器 (WebSocket)
    *   [ ] 搭建 STUN/TURN 中继服务器
2.  **阶段二: Windows 客户端**
    *   [ ] 实现屏幕采集 (DXGI)
    *   [ ] 实现输入注入 (`SendInput`)
    *   [ ] WebRTC 集成
3.  **阶段三: 多平台扩展**
    *   [ ] 开发 macOS 客户端
    *   [ ] 开发 Android 客户端
    *   [ ] 开发 iOS 客户端
4.  **阶段四: 功能完善**
    *   [ ] 文件传输
    *   [ ] 剪贴板同步
    *   [ ] 安全和权限系统

## 6. 如何贡献

(待补充)

## 7. 许可证

(待补充) 